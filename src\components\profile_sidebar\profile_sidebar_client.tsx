"use client";

import React from "react";
import { ProfileContainer } from "./components/profile_container";
import { ProfileAvatar } from "./components/profile_avatar";
import { ProfileInfo } from "./components/profile_info";
import { ProfileBio } from "./components/profile_bio";
import { SocialLinksGrid } from "./components/social_links_grid";
import { ProfileFooter } from "./components/profile_footer";
import { useProfileData } from "./hooks/use_profile_data";
import type { ProfileSidebarClientProps } from "./constants/profile_constants";
import { defaultProps } from "./constants/profile_constants";

/**
 * ProfileSidebarClient - Main profile sidebar component
 */
const ProfileSidebarClient: React.FC<ProfileSidebarClientProps> = ({
  iconSize = defaultProps.iconSize,
  buttonSize = defaultProps.buttonSize,
  borderRadius = defaultProps.borderRadius,
  enhancedEffects = defaultProps.enhancedEffects,
}) => {
  const { profile, hasLocation, hasSocialLinks, hasBio } = useProfileData();

  return (
    <ProfileContainer>
      {/* Avatar with animation */}
      <ProfileAvatar avatarUrl={profile.avatarUrl} name={profile.name} />

      {/* Name, username, and location */}
      <ProfileInfo
        name={profile.name}
        userName={profile.userName}
        location={profile.location}
        hasLocation={hasLocation}
      />

      {/* Bio with better styling */}
      <ProfileBio bio={profile.bio} hasBio={hasBio} />

      {/* Social links with improved styling */}
      <SocialLinksGrid
        socialLinks={profile.socialLinks}
        iconSize={iconSize}
        buttonSize={buttonSize}
        borderRadius={borderRadius}
        enhancedEffects={enhancedEffects}
        hasSocialLinks={hasSocialLinks}
      />

      {/* Footer with improved styling */}
      <ProfileFooter />
    </ProfileContainer>
  );
};

export default ProfileSidebarClient;
