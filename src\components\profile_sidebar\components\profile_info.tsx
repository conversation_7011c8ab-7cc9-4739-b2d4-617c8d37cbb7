"use client";

import React from "react";
import { motion } from "framer-motion";
import { FaMapMarkerAlt } from "react-icons/fa";
import { useProfileAnimations } from "../hooks/use_profile_animations";

interface ProfileInfoProps {
  name: string;
  userName: string;
  location?: string;
  hasLocation?: boolean;
  className?: string;
}

/**
 * ProfileInfo - User information display component
 * Shows the user's name, username, and optional location with proper typography
 */
export const ProfileInfo: React.FC<ProfileInfoProps> = ({
  name,
  userName,
  location,
  hasLocation = false,
  className = "",
}) => {
  const { itemVariants } = useProfileAnimations();

  return (
    <div className={className}>
      {/* Name */}
      <motion.h1
        className="mt-6 mb-2 text-center text-3xl font-bold text-gray-800 dark:text-white"
        variants={itemVariants}
      >
        {name}
      </motion.h1>

      {/* Username */}
      <motion.div
        className="mb-2 flex items-center justify-center gap-2 text-gray-600 dark:text-gray-300"
        variants={itemVariants}
      >
        <span className="text-lg font-medium">{userName}</span>
      </motion.div>

      {/* Location if available */}
      {hasLocation && location && (
        <motion.div
          className="mb-4 flex items-center justify-center gap-1.5 text-gray-500 dark:text-gray-400"
          variants={itemVariants}
        >
          <FaMapMarkerAlt className="h-4 w-4 text-gray-400 dark:text-gray-500" />
          <span className="text-sm">{location}</span>
        </motion.div>
      )}
    </div>
  );
};
