import { useMemo } from "react";
import {
  containerVariants,
  itemVariants,
  socialIconVariants,
  borderRadiusVariants,
  buttonSizeClasses,
  type BorderRadius,
  type ButtonSize,
} from "../constants/profile_constants";

/**
 * Custom hook for managing profile animations and styling
 * Provides animation variants and computed styling classes
 */
export const useProfileAnimations = (
  borderRadius: BorderRadius = "full",
  buttonSize: ButtonSize = "md",
  enhancedEffects: boolean = true,
) => {
  // Memoized styling classes
  const borderRadiusClass = useMemo(() => {
    return borderRadiusVariants[borderRadius];
  }, [borderRadius]);

  const buttonSizeClass = useMemo(() => {
    return buttonSizeClasses[buttonSize];
  }, [buttonSize]);

  const hoverEffects = useMemo(() => {
    return enhancedEffects
      ? "hover:scale-105 hover:shadow-lg transition-all duration-200"
      : "transition-colors duration-200";
  }, [enhancedEffects]);

  return {
    // Animation variants
    containerVariants,
    itemVariants,
    socialIconVariants,
    
    // Computed classes
    borderRadiusClass,
    buttonSizeClass,
    hoverEffects,
  };
};
