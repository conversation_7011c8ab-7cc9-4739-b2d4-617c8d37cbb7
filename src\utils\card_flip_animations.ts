/**
 * Card flip animation utilities and variants for framer-motion
 * Provides smooth 3D flip transitions with proper perspective and timing
 */

import type { Variants } from "framer-motion";

// Animation duration constants
export const FLIP_DURATION = 0.6;
export const FLIP_EASE = [0.25, 0.46, 0.45, 0.94]; // Custom cubic-bezier for smooth flip

// Card flip container variants
export const cardFlipContainerVariants: Variants = {
  front: {
    rotateY: 0,
    transition: {
      duration: FLIP_DURATION,
      ease: FLIP_EASE,
    },
  },
  back: {
    rotateY: 180,
    transition: {
      duration: FLIP_DURATION,
      ease: FLIP_EASE,
    },
  },
};

// Front face variants
export const cardFrontVariants: Variants = {
  front: {
    rotateY: 0,
    opacity: 1,
    transition: {
      duration: FLIP_DURATION,
      ease: FLIP_EASE,
    },
  },
  back: {
    rotateY: 180,
    opacity: 0,
    transition: {
      duration: FLIP_DURATION,
      ease: FLIP_EASE,
    },
  },
};

// Back face variants
export const cardBackVariants: Variants = {
  front: {
    rotateY: -180,
    opacity: 0,
    transition: {
      duration: FLIP_DURATION,
      ease: FLIP_EASE,
    },
  },
  back: {
    rotateY: 0,
    opacity: 1,
    transition: {
      duration: FLIP_DURATION,
      ease: FLIP_EASE,
    },
  },
};

// Button animation variants for smooth transitions
export const flipButtonVariants: Variants = {
  initial: {
    scale: 1,
    opacity: 1,
  },
  hover: {
    scale: 1.02,
    transition: {
      duration: 0.2,
      ease: "easeOut",
    },
  },
  tap: {
    scale: 0.98,
    transition: {
      duration: 0.1,
      ease: "easeOut",
    },
  },
  disabled: {
    opacity: 0.6,
    scale: 1,
    transition: {
      duration: 0.2,
      ease: "easeOut",
    },
  },
};

// Links container animation variants
export const linksContainerVariants: Variants = {
  hidden: {
    opacity: 0,
    y: 20,
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
      ease: "easeOut",
      staggerChildren: 0.1,
      delayChildren: 0.2,
    },
  },
};

// Individual link item animation variants
export const linkItemVariants: Variants = {
  hidden: {
    opacity: 0,
    x: -20,
    scale: 0.95,
  },
  visible: {
    opacity: 1,
    x: 0,
    scale: 1,
    transition: {
      duration: 0.3,
      ease: "easeOut",
    },
  },
  hover: {
    scale: 1.02,
    x: 4,
    transition: {
      duration: 0.2,
      ease: "easeOut",
    },
  },
};

// Close button animation variants
export const closeButtonVariants: Variants = {
  initial: {
    scale: 1,
    rotate: 0,
  },
  hover: {
    scale: 1.1,
    rotate: 90,
    transition: {
      duration: 0.2,
      ease: "easeOut",
    },
  },
  tap: {
    scale: 0.9,
    transition: {
      duration: 0.1,
      ease: "easeOut",
    },
  },
};

// Utility function to get card flip styles
export const getCardFlipStyles = () => ({
  transformStyle: "preserve-3d" as const,
  perspective: "1000px",
  backfaceVisibility: "hidden" as const,
});

// Utility function to get face styles
export const getFaceStyles = () => ({
  position: "absolute" as const,
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  width: "100%",
  height: "100%",
  backfaceVisibility: "hidden" as const,
  WebkitBackfaceVisibility: "hidden" as const,
});

// Animation state type
export type FlipState = "front" | "back";

// Hook for managing flip state
export const useCardFlip = (initialState: FlipState = "front") => {
  const [flipState, setFlipState] = React.useState<FlipState>(initialState);
  const [isFlipping, setIsFlipping] = React.useState(false);

  const flip = React.useCallback(() => {
    if (isFlipping) return;

    setIsFlipping(true);
    setFlipState(prev => prev === "front" ? "back" : "front");

    // Reset flipping state after animation completes
    setTimeout(() => {
      setIsFlipping(false);
    }, FLIP_DURATION * 1000);
  }, [isFlipping]);

  const flipToFront = React.useCallback(() => {
    if (isFlipping || flipState === "front") return;
    flip();
  }, [flip, flipState, isFlipping]);

  const flipToBack = React.useCallback(() => {
    if (isFlipping || flipState === "back") return;
    flip();
  }, [flip, flipState, isFlipping]);

  return {
    flipState,
    isFlipping,
    flip,
    flipToFront,
    flipToBack,
    isShowingFront: flipState === "front",
    isShowingBack: flipState === "back",
  };
};

// Import React for the hook
import React from "react";
