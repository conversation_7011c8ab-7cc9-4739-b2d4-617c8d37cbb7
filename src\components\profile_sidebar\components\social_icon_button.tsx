"use client";

import React from "react";
import { motion } from "framer-motion";
import { But<PERSON> } from "~/components/ui/button";
import { getSocialIcon, type SocialPlatform, type IconSize } from "~/utils/socialIcons";
import { useProfileAnimations } from "../hooks/use_profile_animations";
import type { BorderRadius, ButtonSize } from "../constants/profile_constants";

interface SocialIconButtonProps {
  platform: SocialPlatform;
  url: string;
  label: string;
  iconSize?: IconSize;
  buttonSize?: ButtonSize;
  borderRadius?: BorderRadius;
  enhancedEffects?: boolean;
  className?: string;
}

/**
 * SocialIconButton - Individual social media button component
 * Renders a clickable button with social media icon and proper accessibility
 */
export const SocialIconButton: React.FC<SocialIconButtonProps> = ({
  platform,
  url,
  label,
  iconSize = "xl",
  buttonSize = "md",
  borderRadius = "full",
  enhancedEffects = true,
  className = "",
}) => {
  const { 
    socialIconVariants, 
    borderRadiusClass, 
    buttonSizeClass, 
    hoverEffects 
  } = useProfileAnimations(borderRadius, buttonSize, enhancedEffects);

  const handleClick = () => {
    window.open(url, "_blank", "noopener noreferrer");
  };

  const renderIcon = () => {
    const icon = getSocialIcon(platform, iconSize, "transition-colors duration-200");
    
    if (icon) {
      return icon;
    }
    
    // Fallback to first letter of label if icon not found
    return (
      <span className="text-xl font-semibold">
        {label.charAt(0)}
      </span>
    );
  };

  return (
    <motion.div
      variants={socialIconVariants}
      whileHover="hover"
      whileTap="tap"
      className={className}
    >
      <Button
        variant="outline"
        className={`${buttonSizeClass} ${borderRadiusClass} ${hoverEffects}`}
        onClick={handleClick}
        aria-label={`Visit ${label}`}
      >
        <span className="sr-only">{label}</span>
        {renderIcon()}
      </Button>
    </motion.div>
  );
};
