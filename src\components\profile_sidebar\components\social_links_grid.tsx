"use client";

import React from "react";
import { motion } from "framer-motion";
import type { SocialLink } from "~/types/user";
import type { IconSize } from "~/utils/socialIcons";
import { SocialIconButton } from "./social_icon_button";
import { useProfileAnimations } from "../hooks/use_profile_animations";
import type { BorderRadius, ButtonSize } from "../constants/profile_constants";

interface SocialLinksGridProps {
  socialLinks: SocialLink[];
  iconSize?: IconSize;
  buttonSize?: ButtonSize;
  borderRadius?: BorderRadius;
  enhancedEffects?: boolean;
  hasSocialLinks?: boolean;
  className?: string;
}

/**
 * SocialLinksGrid - Grid layout for social media buttons
 * Displays social media links in a responsive grid with proper spacing
 */
export const SocialLinksGrid: React.FC<SocialLinksGridProps> = ({
  socialLinks,
  iconSize = "xl",
  buttonSize = "md",
  borderRadius = "full",
  enhancedEffects = true,
  hasSocialLinks = true,
  className = "",
}) => {
  const { itemVariants } = useProfileAnimations();

  if (!hasSocialLinks || !socialLinks || socialLinks.length === 0) {
    return null;
  }

  return (
    <motion.div 
      className={`mb-6 w-full ${className}`} 
      variants={itemVariants}
    >
      <div className="flex flex-wrap justify-center gap-3">
        {socialLinks.map((link) => (
          <SocialIconButton
            key={link.platform}
            platform={link.platform}
            url={link.url}
            label={link.label}
            iconSize={iconSize}
            buttonSize={buttonSize}
            borderRadius={borderRadius}
            enhancedEffects={enhancedEffects}
          />
        ))}
      </div>
    </motion.div>
  );
};
