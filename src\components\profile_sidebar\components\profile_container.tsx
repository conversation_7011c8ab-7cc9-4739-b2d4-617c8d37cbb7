"use client";

import React from "react";
import { motion } from "framer-motion";
import { useProfileAnimations } from "../hooks/use_profile_animations";

interface ProfileContainerProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * ProfileContainer - Main wrapper component for the profile sidebar
 * Provides the background gradient, container styling, and entrance animations
 */
export const ProfileContainer: React.FC<ProfileContainerProps> = ({
  children,
  className = "",
}) => {
  const { containerVariants } = useProfileAnimations();

  return (
    <div className="w-full py-8 md:flex md:min-h-screen md:items-center md:justify-center">
      <motion.div
        className={`relative m-6 flex max-w-md flex-col items-center justify-center overflow-hidden rounded-3xl bg-white px-6 py-10 shadow-none sm:px-8 md:m-0 md:px-12 md:py-16 lg:max-w-lg lg:px-16 dark:bg-gray-900 ${className}`}
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        {/* Background gradient accent */}
        <div className="absolute top-0 right-0 left-0 h-32 rounded-t-3xl bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/30 dark:to-purple-950/30" />

        <div className="z-10 mx-auto flex w-full max-w-md flex-col items-center md:mx-auto md:max-w-md md:items-center lg:max-w-lg">
          {children}
        </div>
      </motion.div>
    </div>
  );
};
