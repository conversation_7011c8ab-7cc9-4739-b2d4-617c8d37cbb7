import { useMemo } from "react";
import userDataJson from "~/data/user.json";
import type { UserData } from "~/types/user";

/**
 * Custom hook for managing profile data
 * Handles user data loading, validation, and derived state
 */
export const useProfileData = () => {
  // Cast the imported JSON to the UserData type
  const userData: UserData = userDataJson as UserData;

  const { profile } = userData;

  // Memoized computed values
  const hasLocation = useMemo(() => {
    return Boolean(
      profile.location &&
      profile.location.trim() !== "" &&
      profile.location !== "undefined"
    );
  }, [profile.location]);

  const hasSocialLinks = useMemo(() => {
    return Boolean(profile.socialLinks && profile.socialLinks.length > 0);
  }, [profile.socialLinks]);

  const hasBio = useMemo(() => {
    return Boolean(profile.bio && profile.bio.trim() !== "");
  }, [profile.bio]);

  return {
    userData,
    profile,
    hasLocation,
    hasSocialLinks,
    hasBio,
  };
};
