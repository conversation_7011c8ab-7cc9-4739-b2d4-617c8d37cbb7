// Main component export
export { default } from "./profile_sidebar_client";
export { default as ProfileSidebarClient } from "./profile_sidebar_client";

// Individual component exports for potential reuse
export { ProfileContainer } from "./components/profile_container";
export { ProfileAvatar } from "./components/profile_avatar";
export { ProfileInfo } from "./components/profile_info";
export { ProfileBio } from "./components/profile_bio";
export { SocialLinksGrid } from "./components/social_links_grid";
export { SocialIconButton } from "./components/social_icon_button";
export { ProfileFooter } from "./components/profile_footer";

// Hook exports
export { useProfileData } from "./hooks/use_profile_data";
export { useProfileAnimations } from "./hooks/use_profile_animations";

// Type and constant exports
export type {
  ProfileSidebarClientProps,
  BorderRadius,
  ButtonSize,
} from "./constants/profile_constants";

export {
  defaultProps,
  containerVariants,
  itemVariants,
  socialIconVariants,
  borderRadiusVariants,
  buttonSizeClasses,
} from "./constants/profile_constants";
