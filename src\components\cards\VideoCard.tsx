"use client";

import Image from "next/image";
import React, { useState } from "react";
import { motion } from "framer-motion";
import { type VideoCardData } from "~/types/cards";
import { YouTubeEmbed } from "@next/third-parties/google";
import { Button } from "../ui/button";
import {
  FaYoutube,
  FaPlay,
  FaEye,
  FaClock,
  FaLink,
  FaTimes,
} from "react-icons/fa";
import { LinksDisplay } from "./LinksDisplay";
import {
  useCardFlip,
  cardFlipContainerVariants,
  cardFrontVariants,
  cardBackVariants,
  flipButtonVariants,
  closeButtonVariants,
  getCardFlipStyles,
  getFaceStyles,
} from "~/utils/card_flip_animations";
import { cn } from "~/lib/utils";

interface VideoCardProps {
  card: VideoCardData;
}

// Function to extract YouTube video ID from URL
const extractYouTubeId = (url: string): string | null => {
  // Handle different YouTube URL formats
  const regExp = /^.*(youtu.be\/|v\/|e\/|u\/\w+\/|embed\/|v=)([^#\&\?]*).*/;
  const match = regExp.exec(url);
  return match?.[2] && match[2].length === 11 ? match[2] : null;
};

const VideoCard: React.FC<VideoCardProps> = ({ card }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [imageError, setImageError] = useState(false);
  const videoId =
    card.platform === "youtube" ? extractYouTubeId(card.url) : null;

  // Card flip functionality
  const {
    flipState,
    isFlipping,
    flip,
    flipToFront,
    isShowingFront,
    isShowingBack,
  } = useCardFlip();

  // Check if card has links to show
  const hasLinks = card.descriptionLinks && card.descriptionLinks.length > 0;

  const handlePlayClick = () => {
    setIsPlaying(true);
  };

  const handleExternalClick = () => {
    if (card.url && card.url.trim().length > 0) {
      window.open(card.url, "_blank", "noopener,noreferrer");
    }
  };

  const handleShowLinksClick = () => {
    if (!isFlipping && hasLinks) {
      flip();
    }
  };

  const handleBackToCardClick = () => {
    if (!isFlipping && isShowingBack) {
      flipToFront();
    }
  };

  // Keyboard navigation for accessibility
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Escape" && isShowingBack) {
      handleBackToCardClick();
    }
    // Allow Enter/Space to trigger flip when Show Links button is focused
    if (
      (event.key === "Enter" || event.key === " ") &&
      hasLinks &&
      isShowingFront
    ) {
      const target = event.target as HTMLElement;
      if (target.getAttribute("data-flip-trigger") === "true") {
        event.preventDefault();
        handleShowLinksClick();
      }
    }
  };

  // Focus management for accessibility
  React.useEffect(() => {
    if (isShowingBack) {
      // Focus the close button when flipped to back
      const closeButton = document.querySelector(
        '[data-close-button="true"]',
      ) as HTMLElement;
      if (closeButton) {
        closeButton.focus();
      }
    }
  }, [isShowingBack]);

  return (
    <div
      className="relative h-[520px] max-w-[320px] min-w-[300px]"
      style={getCardFlipStyles()}
      onKeyDown={handleKeyDown}
      tabIndex={-1}
      role="region"
      aria-label={`Video card: ${card.title}`}
    >
      {/* Hidden description for screen readers */}
      <div id="flip-card-description" className="sr-only">
        This card can be flipped to show links from the video description. Use
        the Show Links button or press Enter/Space when focused.
      </div>
      <motion.div
        className="relative h-full w-full"
        variants={cardFlipContainerVariants}
        animate={flipState}
        style={getCardFlipStyles()}
      >
        {/* Front face of the card */}
        <motion.article
          className={cn(
            "group flex h-full w-full transform-gpu flex-col overflow-hidden rounded-2xl bg-white shadow-sm transition-all duration-300 hover:-translate-y-1 hover:shadow-lg dark:bg-gray-800 dark:shadow-gray-900/20",
            isShowingFront ? "pointer-events-auto" : "pointer-events-none",
          )}
          style={getFaceStyles()}
          variants={cardFrontVariants}
          animate={flipState}
          aria-label={`Video: ${card.title} by ${card.channelTitle}`}
        >
          {/* Video thumbnail/embed container */}
          <div className="relative aspect-video w-full overflow-hidden bg-gray-200 dark:bg-gray-900">
            {!isPlaying ? (
              <>
                {/* Thumbnail with play overlay */}
                <div className="relative h-full w-full">
                  <Image
                    src={card.thumbnail}
                    alt={`Thumbnail for ${card.title}`}
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-105"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    priority
                    onError={() => setImageError(true)}
                  />

                  {/* Play button overlay */}
                  <button
                    onClick={handlePlayClick}
                    className="absolute inset-0 flex items-center justify-center bg-black/20 opacity-0 transition-opacity duration-300 hover:opacity-100 focus:opacity-100 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:outline-none"
                    aria-label={`Play video: ${card.title}`}
                  >
                    <div className="flex h-16 w-16 items-center justify-center rounded-full bg-red-600 shadow-lg transition-transform duration-200 hover:scale-110">
                      <FaPlay className="ml-1 text-xl text-white" />
                    </div>
                  </button>

                  {/* Duration badge */}
                  <div className="absolute right-2 bottom-2 flex items-center gap-1 rounded bg-black/80 px-2 py-1 text-xs font-semibold text-white backdrop-blur-sm">
                    <FaClock className="text-[10px]" />
                    <span>{card.duration}</span>
                  </div>
                </div>
              </>
            ) : (
              // YouTube embed when playing
              videoId && (
                <div className="h-full w-full">
                  <YouTubeEmbed
                    videoid={videoId}
                    width={320}
                    height={191}
                    playlabel={`Playing: ${card.title}`}
                    params="controls=1&color=white&autoplay=1"
                  />
                </div>
              )
            )}

            {/* Error state */}
            {imageError && !isPlaying && (
              <div className="flex h-full w-full items-center justify-center bg-gray-300 dark:bg-gray-700">
                <div className="text-center">
                  <FaYoutube className="mx-auto mb-2 text-2xl text-gray-500 dark:text-gray-400" />
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    Thumbnail unavailable
                  </span>
                </div>
              </div>
            )}
          </div>

          {/* Channel avatar container */}
          <div className="relative z-10 h-0">
            {card.channelAvatar && !isPlaying && (
              <div className="absolute -top-8 left-4 transition-all duration-300 group-hover:-top-9">
                <div className="h-16 w-16 overflow-hidden rounded-full border-4 border-white bg-white shadow-lg dark:border-gray-800 dark:bg-gray-800">
                  <Image
                    src={card.channelAvatar}
                    alt={`${card.channelTitle} avatar`}
                    width={64}
                    height={64}
                    className="h-full w-full object-cover"
                    onError={() => {
                      /* Handle avatar error silently */
                    }}
                  />
                </div>
              </div>
            )}
          </div>

          {/* Card content */}
          <div className="flex flex-1 flex-col px-4 pt-10 pb-4">
            {/* Title */}
            <h3 className="mb-3 line-clamp-2 min-h-[48px] text-base leading-tight font-semibold text-gray-900 dark:text-white">
              {card.title}
            </h3>

            {/* Channel name */}
            <div className="mb-2 flex min-h-[20px] items-center">
              <span className="cursor-pointer text-sm font-medium text-gray-600 transition-colors hover:text-gray-800 dark:text-gray-300 dark:hover:text-gray-100">
                {card.channelTitle}
              </span>
            </div>

            {/* Video metadata */}
            <div className="flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400">
              <div className="flex items-center gap-1">
                <FaEye className="text-[10px]" />
                <span>{card.viewCount} views</span>
              </div>
              <span className="text-gray-300 dark:text-gray-600">•</span>
              <span>{card.publishedAt}</span>
            </div>

            {/* Description */}
            {card.description && (
              <p className="mt-3 line-clamp-2 text-xs leading-relaxed text-gray-600 dark:text-gray-400">
                {card.description}
              </p>
            )}
          </div>

          {/* Action buttons */}
          <div className="mt-auto space-y-3 px-4 pb-4">
            {/* Show Links button - only show if links are available */}
            {hasLinks && (
              <motion.div
                variants={flipButtonVariants}
                initial="initial"
                whileHover="hover"
                whileTap="tap"
                animate={isFlipping ? "disabled" : "initial"}
              >
                <Button
                  onClick={handleShowLinksClick}
                  disabled={isFlipping}
                  className={cn(
                    "w-full bg-blue-600 text-white transition-all duration-200 hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
                    isFlipping && "cursor-not-allowed opacity-60",
                  )}
                  size="lg"
                  aria-label={`Show ${card.descriptionLinks?.length} links from video description`}
                  aria-describedby="flip-card-description"
                  data-flip-trigger="true"
                >
                  <FaLink className="mr-2 text-sm" />
                  Show Links ({card.descriptionLinks?.length})
                </Button>
              </motion.div>
            )}

            {/* Watch on YouTube button */}
            <Button
              onClick={handleExternalClick}
              className="w-full bg-red-600 text-white transition-all duration-200 hover:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
              size="lg"
              aria-label={`Watch ${card.title} on YouTube`}
            >
              <FaYoutube className="mr-2 text-sm" />
              Watch on YouTube
            </Button>
          </div>
        </motion.article>

        {/* Back face of the card - Links Display */}
        <motion.article
          className={cn(
            "flex h-full w-full transform-gpu flex-col overflow-hidden rounded-2xl bg-white shadow-sm dark:bg-gray-800",
            isShowingBack ? "pointer-events-auto" : "pointer-events-none",
          )}
          style={getFaceStyles()}
          variants={cardBackVariants}
          animate={flipState}
          aria-label={`Links from ${card.title} description`}
        >
          {/* Header with close button */}
          <div className="flex items-center justify-between border-b border-gray-200 p-4 dark:border-gray-700">
            <div className="flex items-center gap-2">
              <FaLink className="text-blue-600 dark:text-blue-400" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Video Links
              </h3>
            </div>
            <motion.button
              onClick={handleBackToCardClick}
              className={cn(
                "rounded-full p-2 transition-colors duration-200",
                "text-gray-500 hover:bg-gray-100 hover:text-gray-700",
                "dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-200",
                "focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none",
                "dark:focus:ring-offset-gray-800",
              )}
              variants={closeButtonVariants}
              initial="initial"
              whileHover="hover"
              whileTap="tap"
              aria-label="Back to video card"
              data-close-button="true"
            >
              <FaTimes className="h-4 w-4" />
            </motion.button>
          </div>

          {/* Video info summary */}
          <div className="border-b border-gray-200 px-4 py-3 dark:border-gray-700">
            <h4 className="line-clamp-1 text-sm font-medium text-gray-900 dark:text-white">
              {card.title}
            </h4>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              by {card.channelTitle}
            </p>
          </div>

          {/* Links display */}
          <div className="flex-1 overflow-hidden p-4">
            {hasLinks ? (
              <LinksDisplay
                links={card.descriptionLinks!}
                maxLinks={6}
                showDomain={true}
                className="h-full"
              />
            ) : (
              <div className="flex h-full flex-col items-center justify-center text-center">
                <FaLink className="mb-3 text-2xl text-gray-400 dark:text-gray-500" />
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  No links found in video description
                </p>
              </div>
            )}
          </div>

          {/* Back to card button */}
          <div className="px-4 pb-4">
            <Button
              onClick={handleBackToCardClick}
              variant="outline"
              size="lg"
              className="w-full"
              aria-label="Back to video card"
            >
              <FaTimes className="mr-2 text-sm" />
              Back to Video
            </Button>
          </div>
        </motion.article>
      </motion.div>
    </div>
  );
};

export default VideoCard;
