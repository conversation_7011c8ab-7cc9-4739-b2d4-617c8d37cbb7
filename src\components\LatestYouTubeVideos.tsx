"use client";

import React, { useState, useEffect } from "react";
import { type VideoCardData } from "~/types/cards";
import userData<PERSON>son from "~/data/user.json";
import CardFactory from "./cards/CardFactory";
import EmblaCarousel from "./ui/embla-carousel";
import { FaYoutube, FaSpinner } from "react-icons/fa";

// Cast the imported JSON to access the profile data
const typedUserData = userDataJson as any;

interface LatestYouTubeVideosProps {
  maxResults?: number;
  showTitle?: boolean;
  className?: string;
}

const LatestYouTubeVideos: React.FC<LatestYouTubeVideosProps> = ({
  maxResults = 6,
  showTitle = true,
  className = "",
}) => {
  const [videos, setVideos] = useState<VideoCardData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get YouTube channel from user data
  const youtubeChannel = typedUserData.profile.youtube.youtubeChannel;
  const showYouTube = typedUserData.profile.youtube.show;

  useEffect(() => {
    const fetchLatestVideos = async () => {
      // Don't fetch if YouTube is disabled or no channel is configured
      if (!showYouTube || !youtubeChannel) {
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const response = await fetch(
          `/api/youtube/channel?handle=${encodeURIComponent(youtubeChannel)}&limit=${maxResults}`,
        );

        if (!response.ok) {
          throw new Error(`Failed to fetch videos: ${response.statusText}`);
        }

        const data = await response.json();
        setVideos(data.videos || []);
      } catch (err) {
        console.error("Error fetching latest YouTube videos:", err);
        setError("Failed to load latest videos");
      } finally {
        setLoading(false);
      }
    };

    void fetchLatestVideos();
  }, [youtubeChannel, maxResults, showYouTube]);

  // Don't render if YouTube is disabled or no channel is configured
  if (!showYouTube || !youtubeChannel) {
    return null;
  }

  return (
    <section className={`mt-10 flex flex-col gap-3 ${className}`}>
      {showTitle && (
        <div className="flex items-center gap-2">
          <FaYoutube className="text-xl text-red-600" />
          <h3 className="pl-1 text-2xl font-bold text-gray-700 dark:text-gray-300">
            Latest Videos from {youtubeChannel}
          </h3>
        </div>
      )}

      {loading && (
        <div className="flex items-center justify-center py-8">
          <FaSpinner className="animate-spin text-2xl text-gray-500" />
          <span className="ml-2 text-gray-600 dark:text-gray-400">
            Loading latest videos...
          </span>
        </div>
      )}

      {error && (
        <div className="rounded-md bg-red-50 p-4 text-center text-red-700 dark:bg-red-900/20 dark:text-red-400">
          {error}
        </div>
      )}

      {!loading && !error && videos.length > 0 && (
        <EmblaCarousel
          options={{
            align: "start",
            containScroll: "trimSnaps",
            dragFree: false,
            loop: true,
          }}
          showNavigation={false}
          showDots={true}
          gap="gap-4 sm:gap-3"
          className="px-0"
        >
          {videos.map((video) => (
            <div
              key={video.id}
              className="mx-2 max-w-[320px] min-w-[300px] flex-[0_0_auto]"
            >
              <div className="mx-auto my-4 w-full max-w-full overflow-visible">
                <CardFactory card={video} />
              </div>
            </div>
          ))}
        </EmblaCarousel>
      )}

      {!loading && !error && videos.length === 0 && (
        <div className="rounded-md bg-yellow-50 p-4 text-center text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400">
          No videos found for channel {youtubeChannel}
        </div>
      )}
    </section>
  );
};

export default LatestYouTubeVideos;
