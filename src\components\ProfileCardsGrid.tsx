"use client";

import React, { useState } from "react";
import { getCardsByCategory, getCategories } from "~/services/cardService";
import { type Category } from "~/types/cards";
import CardFactory from "./cards/CardFactory";
import EmblaCarousel from "./ui/embla-carousel";
import LatestYouTubeVideos from "./LatestYouTubeVideos";

const ProfileCardsGrid: React.FC = () => {
  // Get categories from the service
  const categories: Category[] = getCategories();

  const [activeCategory, setActiveCategory] = useState("all");

  // Get filtered cards based on active category
  const filteredCards = getCardsByCategory(activeCategory);

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h2 className="text-4xl font-bold text-gray-800 dark:text-gray-100">
          My Directory
        </h2>
        <div className="flex flex-wrap gap-2">
          {categories.map((category) => (
            <button
              key={category.id}
              className={`focus:ring-primary-400 rounded-full px-4 py-1.5 text-sm font-medium transition focus:ring-2 focus:outline-none ${
                activeCategory === category.id
                  ? "bg-primary dark:bg-primary/90 dark:text-background text-white"
                  : "bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
              }`}
              onClick={() => setActiveCategory(category.id)}
              aria-pressed={activeCategory === category.id}
              aria-label={`Filter by ${category.name}`}
            >
              {category.name}
            </button>
          ))}
        </div>
      </div>

      {activeCategory === "all" ? (
        <div className="flex flex-col gap-6">
          {/* Latest YouTube Videos Section */}
          <LatestYouTubeVideos maxResults={6} />

          {categories
            .filter((cat) => cat.id !== "all")
            .map((category) => {
              const cards = getCardsByCategory(category.id);
              if (!cards.length) return null;
              return (
                <section
                  key={category.id}
                  className="mt-10 flex flex-col gap-3"
                >
                  <h3 className="pl-1 text-2xl font-bold text-gray-700 dark:text-gray-300">
                    {category.name}
                  </h3>
                  <EmblaCarousel
                    options={{
                      align: "start",
                      containScroll: "trimSnaps",
                      dragFree: false,
                      loop: true,
                    }}
                    showNavigation={false}
                    showDots={true}
                    gap="gap-4 sm:gap-3"
                    className="px-0"
                  >
                    {cards.map((card) => (
                      <div
                        key={card.id}
                        className="mx-2 max-w-[320px] min-w-[300px] flex-[0_0_auto]"
                      >
                        <div className="mx-auto my-4 w-full max-w-full overflow-visible">
                          <CardFactory card={card} />
                        </div>
                      </div>
                    ))}
                  </EmblaCarousel>
                </section>
              );
            })}
        </div>
      ) : (
        <>
          <div className="block sm:hidden">
            {/* Mobile: Carousel view */}
            <EmblaCarousel
              options={{
                align: "start",
                containScroll: "trimSnaps",
                dragFree: false,
                loop: true,
              }}
              showNavigation={false}
              showDots={true}
              gap="gap-4 sm:gap-3"
            >
              {filteredCards.map((card) => (
                <div
                  key={card.id}
                  className="max-w-[320px] min-w-[300px] flex-[0_0_auto] px-2"
                >
                  <div className="mx-auto my-4 w-full max-w-full overflow-visible">
                    <CardFactory card={card} />
                  </div>
                </div>
              ))}
            </EmblaCarousel>
          </div>
          <div className="hidden sm:block">
            {/* Desktop: Grid view */}
            <div className="grid grid-cols-2 gap-3 lg:grid-cols-3">
              {filteredCards.map((card) => (
                <div
                  key={card.id}
                  className="mx-auto w-full max-w-full overflow-visible lg:max-w-[400px]"
                >
                  <CardFactory card={card} />
                </div>
              ))}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default ProfileCardsGrid;
