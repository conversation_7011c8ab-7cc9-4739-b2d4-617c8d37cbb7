// Card category type
export interface Category {
  id: string;
  name: string;
}

// Project card interface
export interface ProjectCardData {
  id: string;
  type: "project";
  category: string;
  title: string;
  subtitle?: string;
  price: string;
  chartColor: string;
  chartStroke: string;
  chartFill: string;
  chartPoints: string;
  url: string;
  iconEmoji?: string;
}

// App card interface - specific for app type only
export interface AppCardData {
  id: string;
  type: "app";
  category: string;
  title: string;
  subtitle?: string;
  description?: string;
  price: {
    free: boolean;
    cuponCode?: string;
  };
  tags: string[];
  platforms: string[];
  logo: string;
  image: string;
  url: string;
  developer: string;
  updated_at: string;
}

// Video card interface
export interface VideoCardData {
  id: string;
  type: "video";
  category: string;
  title: string;
  description?: string;
  thumbnail: string;
  channelAvatar: string;
  duration: string;
  channelTitle: string;
  channelId: string;
  viewCount: string;
  publishedAt: string;
  platform: "youtube" | "vimeo" | "other";
  url: string;
  descriptionLinks?: string[]; // Optional array of links extracted from video description
}

// Article card interface
export interface ArticleCardData {
  id: string;
  type: "article";
  category: string;
  title: string;
  subtitle?: string;
  price: string;
  coverImage: string;
  excerpt: string;
  author: string;
  publishedAt: string;
  readTime: string;
  platform: "medium" | "wordpress" | "other";
  url: string;
}

// GitHub repository card interface
export interface GitHubRepoCardData {
  id: string;
  type: "github";
  category: string;
  avatar: string;
  repoUrl: string;
  title: string;
  subtitle?: string;
  description: string;
  owner: string;
  repoName: string;
  language: string;
  stars: number;
  forks: number;
  issues: number;
  updatedAt: string;
}

// Video channel card interface
export interface VideoChannelCardData {
  id: string;
  type: "channel";
  category: string;
  channelTitle: string;
  subtitle: string;
  description: string;
  channelAvatar: string;
  bannerImage: string;
  subscriberCount: string;
  videoCount: string;
  viewCount: string;
  publishedAt: string;
  country: string;
  platform: "youtube" | "vimeo" | "other";
  url: string;
}

// Union type for all card types
export type CardData = ProjectCardData | AppCardData | VideoCardData | ArticleCardData | GitHubRepoCardData | VideoChannelCardData;

// Cards data structure
export interface CardsData {
  categories: Category[];
  cards: {
    projects: ProjectCardData[];
    apps: AppCardData[];
    "yt-videos": VideoCardData[];
    "yt-channels": VideoChannelCardData[];
    articles: ArticleCardData[];
    github: GitHubRepoCardData[];
  };
}
