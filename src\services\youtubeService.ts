
import type { VideoCardData, VideoChannelCardData } from "~/types/cards";
import userDataJson from "~/data/user.json";
import type { UserData } from "~/types/user";
import { type YouTubeChannelInfo, type YouTubeVideoDetails } from '~/types/youtube';
import { extractUrls } from "~/utils/textUtils";

// Cast the imported JSON to the UserData type
const typedUserData: UserData = userDataJson as UserData;

// Constants
const YOUTUBE_API_BASE_URL = "https://www.googleapis.com/youtube/v3";
const DEFAULT_MAX_RESULTS = 6;
const DEFAULT_REGION_CODE = "BR";

/**
 * Fetches channel avatar URL by channel ID
 * @param channelId The YouTube channel ID
 * @returns Channel avatar URL or empty string if not found
 */
const getChannelAvatarById = async (channelId: string): Promise<string> => {
  if (!channelId?.trim()) {
    return "";
  }

  try {
    const response = await fetch(
      `${YOUTUBE_API_BASE_URL}/channels?part=snippet&id=${channelId}&key=${process.env.YOUTUBE_API_KEY}`
    );

    if (!response.ok) {
      console.warn(`Failed to fetch channel avatar for ID ${channelId}: ${response.statusText}`);
      return "";
    }

    const data = await response.json() as {
      items?: {
        snippet: {
          thumbnails: {
            default?: { url: string };
            medium?: { url: string };
            high?: { url: string };
          };
        };
      }[];
    };

    if (!data.items || data.items.length === 0) {
      return "";
    }

    const thumbnails = data.items[0]?.snippet?.thumbnails;
    return thumbnails?.high?.url ?? thumbnails?.medium?.url ?? thumbnails?.default?.url ?? "";
  } catch (error) {
    console.warn(`Error fetching channel avatar for ID ${channelId}:`, error);
    return "";
  }
};

/**
 * Fetches channel avatars for multiple channel IDs in a single API call
 * @param channelIds Array of YouTube channel IDs
 * @returns Map of channelId to avatar URL
 */
const getChannelAvatarsByIds = async (channelIds: string[]): Promise<Map<string, string>> => {
  const avatarMap = new Map<string, string>();

  if (!channelIds.length) {
    return avatarMap;
  }

  try {
    const uniqueChannelIds = [...new Set(channelIds.filter(id => id?.trim()))];
    if (!uniqueChannelIds.length) {
      return avatarMap;
    }

    const response = await fetch(
      `${YOUTUBE_API_BASE_URL}/channels?part=snippet&id=${uniqueChannelIds.join(',')}&key=${process.env.YOUTUBE_API_KEY}`
    );

    if (!response.ok) {
      console.warn(`Failed to fetch channel avatars: ${response.statusText}`);
      return avatarMap;
    }

    const data = await response.json() as {
      items?: {
        id: string;
        snippet: {
          thumbnails: {
            default?: { url: string };
            medium?: { url: string };
            high?: { url: string };
          };
        };
      }[];
    };

    if (data.items) {
      data.items.forEach(item => {
        const thumbnails = item.snippet?.thumbnails;
        const avatarUrl = thumbnails?.high?.url ?? thumbnails?.medium?.url ?? thumbnails?.default?.url ?? "";
        avatarMap.set(item.id, avatarUrl);
      });
    }

    return avatarMap;
  } catch (error) {
    console.warn("Error fetching channel avatars:", error);
    return avatarMap;
  }
};

// Helper function to create video card data
const createVideoCardData = (
  id: string,
  title: string,
  description: string,
  thumbnailUrl: string,
  duration: string,
  channelTitle: string,
  channelId: string,
  viewCount: string,
  publishedAt: string,
  url: string,
  channelAvatar = ""
): VideoCardData => {
  // Extract links from video description
  const descriptionLinks = extractUrls(description);

  return {
    id,
    type: "video",
    category: "yt-videos",
    title,
    description,
    thumbnail: thumbnailUrl,
    channelAvatar,
    duration,
    channelTitle,
    channelId,
    viewCount,
    publishedAt,
    platform: "youtube",
    url,
    descriptionLinks: descriptionLinks.length > 0 ? descriptionLinks : undefined,
  };
};

// Helper function to create channel card data
const createChannelCardData = (channelInfo: YouTubeChannelInfo): VideoChannelCardData => ({
  id: channelInfo.id,
  type: "channel",
  category: "yt-channels",
  channelTitle: channelInfo.title,
  subtitle: channelInfo.customUrl ?? "",
  description: channelInfo.description,
  channelAvatar: channelInfo.thumbnails.high?.url ?? channelInfo.thumbnails.medium?.url ?? channelInfo.thumbnails.default?.url ?? "",
  bannerImage: channelInfo.brandingSettings?.image?.bannerExternalUrl ?? "",
  subscriberCount: formatViews(channelInfo.statistics.subscriberCount),
  videoCount: channelInfo.statistics.videoCount,
  viewCount: formatViews(channelInfo.statistics.viewCount),
  publishedAt: formatDate(channelInfo.publishedAt),
  country: channelInfo.brandingSettings?.channel?.country ?? "",
  platform: "youtube",
  url: `https://www.youtube.com/channel/${channelInfo.id}`,
});

// Enhanced interfaces for YouTube API responses
interface YouTubeSearchResponse {
  items: {
    id: {
      videoId: string;
    };
    snippet: {
      title: string;
      description: string;
      thumbnails: {
        default?: { url: string; width: number; height: number };
        medium?: { url: string; width: number; height: number };
        high: { url: string; width: number; height: number };
        standard?: { url: string; width: number; height: number };
        maxres?: { url: string; width: number; height: number };
      };
      channelTitle: string;
      channelId: string;
      publishedAt: string;
    };
  }[];
}

interface YouTubeVideoDetailsResponse {
  items: {
    id: string;
    contentDetails: {
      duration: string;
    };
    statistics: {
      viewCount: string;
    };
  }[];
}

interface YouTubeChannelApiResponse {
  items?: {
    id: string;
    snippet: {
      title: string;
      description: string;
      publishedAt: string;
      thumbnails: {
        default?: { url: string; width: number; height: number };
        medium?: { url: string; width: number; height: number };
        high: { url: string; width: number; height: number };
      };
      country?: string;
      customUrl?: string;
    };
    statistics: {
      subscriberCount?: string;
      videoCount?: string;
      viewCount?: string;
    };
    brandingSettings?: {
      channel?: {
        title?: string;
        description?: string;
        keywords?: string;
        trackingAnalyticsAccountId?: string;
        unsubscribedTrailer?: string;
      };
      image?: {
        bannerExternalUrl?: string;
      };
    };
  }[];
}

/**
 * Formata a duração do vídeo do formato ISO 8601 para um formato legível
 * @param isoDuration Duração no formato ISO 8601 (PT1H2M3S)
 * @returns Duração formatada (1:02:03)
 */
const formatDuration = (isoDuration: string): string => {
  const match = /PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/.exec(isoDuration);
  if (!match) return "0:00";

  const hours = match[1] ? parseInt(match[1]) : 0;
  const minutes = match[2] ? parseInt(match[2]) : 0;
  const seconds = match[3] ? parseInt(match[3]) : 0;

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
  }
  return `${minutes}:${seconds.toString().padStart(2, "0")}`;
};

/**
 * Formata o número de visualizações
 * @param viewCount Número de visualizações
 * @returns Visualizações formatadas (1.2M, 3.4K, etc.)
 */
const formatViews = (viewCount: string): string => {
  const count = parseInt(viewCount);
  if (count >= 1000000) {
    return `${(count / 1000000).toFixed(1)}M`;
  }
  if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}K`;
  }
  return count.toString();
};

/**
 * Formata a data de publicação
 * @param publishedAt Data de publicação
 * @returns Tempo relativo (1 day ago, 2 weeks ago, etc.)
 */
const formatDate = (publishedAt: string): string => {
  const published = new Date(publishedAt);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - published.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 1) return "1 day ago";
  if (diffDays < 7) return `${diffDays} days ago`;
  if (diffDays < 14) return "1 week ago";
  if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
  if (diffDays < 60) return "1 month ago";
  if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
  if (diffDays < 730) return "1 year ago";
  return `${Math.floor(diffDays / 365)} years ago`;
};

/**
 * Busca vídeos no YouTube
 * @param query Termo de busca
 * @param maxResults Número máximo de resultados (padrão: 6)
 * @returns Array de VideoCardData
 */
export const searchYouTubeVideos = async (
  query: string,
  maxResults = DEFAULT_MAX_RESULTS,
): Promise<VideoCardData[]> => {
  if (!query.trim()) {
    console.warn("searchYouTubeVideos: query is empty");
    return [];
  }

  try {
    // Busca os vídeos
    const searchResponse = await fetch(
      `${YOUTUBE_API_BASE_URL}/search?part=snippet&q=${encodeURIComponent(
        query,
      )}&maxResults=${maxResults}&type=video&key=${process.env.YOUTUBE_API_KEY}`,
    );

    if (!searchResponse.ok) {
      throw new Error(`YouTube API error: ${searchResponse.statusText}`);
    }

    const searchData = (await searchResponse.json()) as YouTubeSearchResponse;

    if (!searchData.items || searchData.items.length === 0) {
      return [];
    }

    // Obtém os IDs dos vídeos para buscar detalhes adicionais
    const videoIds = searchData.items.map((item) => item.id.videoId).join(",");

    // Busca detalhes dos vídeos (duração, visualizações, etc.)
    const videoDetailsResponse = await fetch(
      `${YOUTUBE_API_BASE_URL}/videos?part=contentDetails,statistics&id=${videoIds}&key=${process.env.YOUTUBE_API_KEY}`,
    );

    if (!videoDetailsResponse.ok) {
      throw new Error(`YouTube API error: ${videoDetailsResponse.statusText}`);
    }

    const videoDetails = (await videoDetailsResponse.json()) as YouTubeVideoDetailsResponse;

    // Cria um mapa de detalhes de vídeo por ID para facilitar o acesso
    const videoDetailsMap = new Map<string, YouTubeVideoDetailsResponse['items'][0]>();
    videoDetails.items.forEach(detail => {
      videoDetailsMap.set(detail.id, detail);
    });

    // Obtém os IDs únicos dos canais para buscar avatares
    const channelIds = [...new Set(searchData.items.map(item => item.snippet.channelId))];

    // Busca avatares dos canais
    const channelAvatarMap = await getChannelAvatarsByIds(channelIds);

    // Mapeia os resultados para o formato VideoCardData
    return searchData.items.map((item) => {
      const details = videoDetailsMap.get(item.id.videoId);
      const channelAvatar = channelAvatarMap.get(item.snippet.channelId) ?? "";

      return createVideoCardData(
        item.id.videoId,
        item.snippet.title,
        item.snippet.description,
        item.snippet.thumbnails.high.url,
        details?.contentDetails?.duration ? formatDuration(details.contentDetails.duration) : "0:00",
        item.snippet.channelTitle,
        item.snippet.channelId,
        details?.statistics?.viewCount ? formatViews(details.statistics.viewCount) : "0",
        formatDate(item.snippet.publishedAt),
        `https://www.youtube.com/watch?v=${item.id.videoId}`,
        channelAvatar
      );
    });
  } catch (error) {
    console.error("Error fetching YouTube videos:", error);
    return [];
  }
};

/**
 * Fetches detailed information for a specific YouTube video by its ID, including channel avatar.
 * @param videoId The ID of the YouTube video.
 * @returns A Promise that resolves to YouTubeVideoDetails with channel avatar or null if not found or an error occurs.
 */
export const getVideoDetailsById = async (videoId: string): Promise<(YouTubeVideoDetails & { channelAvatar?: string }) | null> => {
  if (!videoId?.trim()) {
    console.error("getVideoDetailsById: videoId is required");
    return null;
  }

  try {
    const apiUrl = `${YOUTUBE_API_BASE_URL}/videos?part=snippet,contentDetails,statistics,player&id=${videoId}&key=${process.env.YOUTUBE_API_KEY}`;
    const response = await fetch(apiUrl);

    if (!response.ok) {
      console.error(`YouTube API error in getVideoDetailsById: ${response.status} ${response.statusText}`);
      return null;
    }

    const data = await response.json() as {
      items?: YouTubeVideoDetails[];
    };

    if (!data.items || data.items.length === 0) {
      console.warn(`No video found with ID: ${videoId}`);
      return null;
    }

    const videoData = data.items[0];

    if (!videoData) {
      return null;
    }

    // Get the channel avatar using the channelId from video data
    const channelAvatar = await getChannelAvatarById(videoData.snippet.channelId);

    // Return video data with channel avatar
    return {
      ...videoData,
      channelAvatar
    };

  } catch (error) {
    console.error(`Error fetching video details for ID ${videoId}:`, error);
    return null;
  }
};

/**
 * Busca vídeos populares no YouTube
 * @param maxResults Número máximo de resultados (padrão: 6)
 * @param regionCode Código da região (padrão: BR)
 * @returns Array de VideoCardData
 */
export const getPopularYouTubeVideos = async (
  maxResults = DEFAULT_MAX_RESULTS,
  regionCode = DEFAULT_REGION_CODE,
): Promise<VideoCardData[]> => {
  try {
    // Busca vídeos populares
    const popularResponse = await fetch(
      `${YOUTUBE_API_BASE_URL}/videos?part=snippet,contentDetails,statistics&chart=mostPopular&regionCode=${regionCode}&maxResults=${maxResults}&key=${process.env.YOUTUBE_API_KEY}`,
    );

    if (!popularResponse.ok) {
      throw new Error(`YouTube API error: ${popularResponse.statusText}`);
    }

    const popularData = await popularResponse.json() as {
      items: {
        id: string;
        snippet: {
          title: string;
          description: string;
          channelTitle: string;
          channelId: string;
          publishedAt: string;
          thumbnails: {
            high: {
              url: string;
            };
          };
        };
        contentDetails: {
          duration: string;
        };
        statistics: {
          viewCount: string;
        };
      }[];
    };

    if (!popularData.items || popularData.items.length === 0) {
      return [];
    }

    // Obtém os IDs únicos dos canais para buscar avatares
    const channelIds = [...new Set(popularData.items.map(item => item.snippet.channelId))];

    // Busca avatares dos canais
    const channelAvatarMap = await getChannelAvatarsByIds(channelIds);

    // Mapeia os resultados para o formato VideoCardData
    return popularData.items.map((item) => {
      const channelAvatar = channelAvatarMap.get(item.snippet.channelId) ?? "";

      return createVideoCardData(
        item.id,
        item.snippet.title,
        item.snippet.description,
        item.snippet.thumbnails.high.url,
        item.contentDetails?.duration ? formatDuration(item.contentDetails.duration) : "0:00",
        item.snippet.channelTitle,
        item.snippet.channelId,
        item.statistics?.viewCount ? formatViews(item.statistics.viewCount) : "0",
        formatDate(item.snippet.publishedAt),
        `https://www.youtube.com/watch?v=${item.id}`,
        channelAvatar
      );
    });
  } catch (error) {
    console.error("Error fetching popular YouTube videos:", error);
    return [];
  }
};

/**
 * Busca informações de um canal do YouTube pelo nome de usuário ou handle
 * @param channelHandle Nome de usuário ou handle do canal (ex: @channelname). Optional.
 * @returns Informações do canal ou null se não encontrado
 */
export const getChannelInfo = async (channelHandle?: string): Promise<YouTubeChannelInfo | null> => {
  try {
    const effectiveChannelHandle = channelHandle ?? typedUserData.profile.youtube.youtubeChannel;

    if (!effectiveChannelHandle) {
      console.error("Channel handle is not defined in user.json or as a parameter.");
      return null;
    }

    // Remover o @ se estiver presente
    const handle = typeof effectiveChannelHandle === 'string' && effectiveChannelHandle.startsWith('@')
      ? effectiveChannelHandle.substring(1)
      : effectiveChannelHandle;

    // Primeiro, tentamos buscar pelo handle
    const searchResponse = await fetch(
      `${YOUTUBE_API_BASE_URL}/search?part=snippet&q=${encodeURIComponent(
        '@' + handle // Ensure @ is prefixed for handle search
      )}&type=channel&maxResults=1&key=${process.env.YOUTUBE_API_KEY}`
    );

    if (!searchResponse.ok) {
      throw new Error(`YouTube API error: ${searchResponse.statusText}`);
    }

    const searchData = await searchResponse.json() as {
      items?: {
        id: {
          channelId: string;
        };
        snippet: {
          title: string;
          description: string;
        };
      }[];
    };

    if (!searchData.items || searchData.items.length === 0) {
      return null;
    }

    // Obtém o ID do canal
    const channelId = searchData.items[0]?.id?.channelId;

    if (!channelId) {
      return null;
    }

    // Busca informações detalhadas do canal
    const channelResponse = await fetch(
      `${YOUTUBE_API_BASE_URL}/channels?part=snippet,statistics,brandingSettings&id=${channelId}&key=${process.env.YOUTUBE_API_KEY}`
    );

    if (!channelResponse.ok) {
      throw new Error(`YouTube API error: ${channelResponse.statusText}`);
    }

    const channelData = await channelResponse.json() as YouTubeChannelApiResponse;

    if (!channelData.items || channelData.items.length === 0) {
      return null;
    }

    const channel = channelData.items[0];

    if (!channel) {
      return null;
    }

    // Extrair a data de criação do canal
    const publishedAt = channel.snippet.publishedAt;

    // Converter thumbnails para o formato correto
    const thumbnails: YouTubeChannelInfo['thumbnails'] = {};
    if (channel.snippet.thumbnails.default) {
      thumbnails.default = {
        url: channel.snippet.thumbnails.default.url,
        width: channel.snippet.thumbnails.default.width,
        height: channel.snippet.thumbnails.default.height,
      };
    }
    if (channel.snippet.thumbnails.medium) {
      thumbnails.medium = {
        url: channel.snippet.thumbnails.medium.url,
        width: channel.snippet.thumbnails.medium.width,
        height: channel.snippet.thumbnails.medium.height,
      };
    }
    if (channel.snippet.thumbnails.high) {
      thumbnails.high = {
        url: channel.snippet.thumbnails.high.url,
        width: channel.snippet.thumbnails.high.width,
        height: channel.snippet.thumbnails.high.height,
      };
    }

    const channelInfo: YouTubeChannelInfo = {
      id: channel.id,
      title: channel.snippet.title,
      description: channel.snippet.description,
      customUrl: channel.snippet.customUrl ?? `@${handle}`,
      publishedAt: publishedAt,
      thumbnails: thumbnails,
      statistics: {
        viewCount: channel.statistics.viewCount ?? '0',
        subscriberCount: channel.statistics.subscriberCount ?? '0',
        hiddenSubscriberCount: false, // YouTube API doesn't provide this field directly
        videoCount: channel.statistics.videoCount ?? '0',
      },
      brandingSettings: {
        channel: {
          title: channel.brandingSettings?.channel?.title,
          description: channel.brandingSettings?.channel?.description,
          keywords: channel.brandingSettings?.channel?.keywords,
          trackingAnalyticsAccountId: channel.brandingSettings?.channel?.trackingAnalyticsAccountId,
          unsubscribedTrailer: channel.brandingSettings?.channel?.unsubscribedTrailer,
          country: channel.snippet.country,
        },
        image: {
          bannerExternalUrl: channel.brandingSettings?.image?.bannerExternalUrl,
        },
      },
    };
    return channelInfo;
  } catch (error) {
    console.error("Error fetching channel info:", error);
    return null;
  }
};

export const getChannelVideos = async (
  channelIdInput?: string,
  maxResults = DEFAULT_MAX_RESULTS,
): Promise<VideoCardData[]> => {
  let channelId = channelIdInput;

  try {
    // If channelId is not provided, try to get it using the handle from user.json
    if (!channelId) {
      const youtubeChannel = typedUserData.profile.youtube.youtubeChannel;
      if (youtubeChannel) {
        const channelInfo = await getChannelInfo(youtubeChannel);
        if (channelInfo?.id) {
          channelId = channelInfo.id;
        } else {
          console.error("Could not retrieve channelId using user.json handle for getChannelVideos.");
          return [];
        }
      } else {
        console.error("channelId not provided and no channelHandle in user.json for getChannelVideos.");
        return [];
      }
    }

    // Busca os vídeos do canal
    const channelResponse = await fetch(
      `${YOUTUBE_API_BASE_URL}/search?part=snippet&channelId=${channelId}&maxResults=${maxResults}&order=date&type=video&key=${process.env.YOUTUBE_API_KEY}`,
    );

    if (!channelResponse.ok) {
      throw new Error(`YouTube API error: ${channelResponse.statusText}`);
    }

    const channelData = (await channelResponse.json()) as YouTubeSearchResponse;

    if (!channelData.items || channelData.items.length === 0) {
      return [];
    }

    // Obtém os IDs dos vídeos para buscar detalhes adicionais
    const videoIds = channelData.items.map((item) => item.id.videoId).join(",");

    // Busca detalhes dos vídeos (duração, visualizações, etc.)
    const videoDetailsResponse = await fetch(
      `${YOUTUBE_API_BASE_URL}/videos?part=contentDetails,statistics&id=${videoIds}&key=${process.env.YOUTUBE_API_KEY}`,
    );

    if (!videoDetailsResponse.ok) {
      throw new Error(`YouTube API error: ${videoDetailsResponse.statusText}`);
    }

    const videoDetails = (await videoDetailsResponse.json()) as YouTubeVideoDetailsResponse;

    // Cria um mapa de detalhes de vídeo por ID para facilitar o acesso
    const videoDetailsMap = new Map<string, YouTubeVideoDetailsResponse['items'][0]>();
    videoDetails.items.forEach(detail => {
      videoDetailsMap.set(detail.id, detail);
    });

    // Obtém os IDs únicos dos canais para buscar avatares (neste caso, será apenas um canal)
    const channelIds = [...new Set(channelData.items.map(item => item.snippet.channelId))];

    // Busca avatares dos canais
    const channelAvatarMap = await getChannelAvatarsByIds(channelIds);

    // Mapeia os resultados para o formato VideoCardData
    return channelData.items.map((item) => {
      const details = videoDetailsMap.get(item.id.videoId);
      const channelAvatar = channelAvatarMap.get(item.snippet.channelId) ?? "";

      return createVideoCardData(
        item.id.videoId,
        item.snippet.title,
        item.snippet.description,
        item.snippet.thumbnails.high.url,
        details?.contentDetails?.duration ? formatDuration(details.contentDetails.duration) : "0:00",
        item.snippet.channelTitle,
        item.snippet.channelId,
        details?.statistics?.viewCount ? formatViews(details.statistics.viewCount) : "0",
        formatDate(item.snippet.publishedAt),
        `https://www.youtube.com/watch?v=${item.id.videoId}`,
        channelAvatar
      );
    });
  } catch (error) {
    console.error("Error fetching channel videos:", error);
    return [];
  }
};

/**
 * Busca canais do YouTube e retorna como VideoChannelCardData
 * @param query Termo de busca para canais
 * @param maxResults Número máximo de resultados (padrão: 6)
 * @returns Array de VideoChannelCardData
 */
export const searchYouTubeChannels = async (
  query: string,
  maxResults = DEFAULT_MAX_RESULTS,
): Promise<VideoChannelCardData[]> => {
  if (!query.trim()) {
    console.warn("searchYouTubeChannels: query is empty");
    return [];
  }

  try {
    // Busca canais
    const searchResponse = await fetch(
      `${YOUTUBE_API_BASE_URL}/search?part=snippet&q=${encodeURIComponent(
        query,
      )}&maxResults=${maxResults}&type=channel&key=${process.env.YOUTUBE_API_KEY}`,
    );

    if (!searchResponse.ok) {
      throw new Error(`YouTube API error: ${searchResponse.statusText}`);
    }

    const searchData = await searchResponse.json() as {
      items?: {
        id: {
          channelId: string;
        };
        snippet: {
          title: string;
          description: string;
        };
      }[];
    };

    if (!searchData.items || searchData.items.length === 0) {
      return [];
    }

    // Obtém os IDs dos canais para buscar informações detalhadas
    const channelIds = searchData.items.map((item) => item.id.channelId).join(",");

    // Busca informações detalhadas dos canais
    const channelDetailsResponse = await fetch(
      `${YOUTUBE_API_BASE_URL}/channels?part=snippet,statistics,brandingSettings&id=${channelIds}&key=${process.env.YOUTUBE_API_KEY}`,
    );

    if (!channelDetailsResponse.ok) {
      throw new Error(`YouTube API error: ${channelDetailsResponse.statusText}`);
    }

    const channelDetails = await channelDetailsResponse.json() as YouTubeChannelApiResponse;

    if (!channelDetails.items || channelDetails.items.length === 0) {
      return [];
    }

    // Mapeia os resultados para o formato VideoChannelCardData
    return channelDetails.items.map((channel) => {
      // Converter thumbnails para o formato correto
      const thumbnails: YouTubeChannelInfo['thumbnails'] = {};
      if (channel.snippet.thumbnails.default) {
        thumbnails.default = {
          url: channel.snippet.thumbnails.default.url,
          width: channel.snippet.thumbnails.default.width,
          height: channel.snippet.thumbnails.default.height,
        };
      }
      if (channel.snippet.thumbnails.medium) {
        thumbnails.medium = {
          url: channel.snippet.thumbnails.medium.url,
          width: channel.snippet.thumbnails.medium.width,
          height: channel.snippet.thumbnails.medium.height,
        };
      }
      if (channel.snippet.thumbnails.high) {
        thumbnails.high = {
          url: channel.snippet.thumbnails.high.url,
          width: channel.snippet.thumbnails.high.width,
          height: channel.snippet.thumbnails.high.height,
        };
      }

      const channelInfo: YouTubeChannelInfo = {
        id: channel.id,
        title: channel.snippet.title,
        description: channel.snippet.description,
        customUrl: channel.snippet.customUrl,
        publishedAt: channel.snippet.publishedAt,
        thumbnails: thumbnails,
        statistics: {
          viewCount: channel.statistics.viewCount ?? '0',
          subscriberCount: channel.statistics.subscriberCount ?? '0',
          hiddenSubscriberCount: false,
          videoCount: channel.statistics.videoCount ?? '0',
        },
        brandingSettings: {
          channel: {
            title: channel.brandingSettings?.channel?.title,
            description: channel.brandingSettings?.channel?.description,
            keywords: channel.brandingSettings?.channel?.keywords,
            trackingAnalyticsAccountId: channel.brandingSettings?.channel?.trackingAnalyticsAccountId,
            unsubscribedTrailer: channel.brandingSettings?.channel?.unsubscribedTrailer,
            country: channel.snippet.country,
          },
          image: {
            bannerExternalUrl: channel.brandingSettings?.image?.bannerExternalUrl,
          },
        },
      };

      return createChannelCardData(channelInfo);
    });
  } catch (error) {
    console.error("Error fetching YouTube channels:", error);
    return [];
  }
};
