"use client";

import React from "react";
import { motion } from "framer-motion";
import { useProfileAnimations } from "../hooks/use_profile_animations";

interface ProfileBioProps {
  bio: string;
  hasBio?: boolean;
  className?: string;
}

/**
 * ProfileBio - User biography display component
 * Shows the user's bio text with proper typography and spacing
 */
export const ProfileBio: React.FC<ProfileBioProps> = ({
  bio,
  hasBio = true,
  className = "",
}) => {
  const { itemVariants } = useProfileAnimations();

  if (!hasBio || !bio.trim()) {
    return null;
  }

  return (
    <motion.div 
      className={`mb-8 w-full ${className}`} 
      variants={itemVariants}
    >
      <p className="text-center leading-relaxed text-gray-600 dark:text-gray-300">
        {bio}
      </p>
    </motion.div>
  );
};
