"use client";

import React from "react";
import { motion } from "framer-motion";
import { ThemeToggle } from "~/components/ui/ThemeToggle";
import { useProfileAnimations } from "../hooks/use_profile_animations";

interface ProfileFooterProps {
  className?: string;
}

/**
 * ProfileFooter - Footer component with MyDir branding and theme toggle
 * Displays the MyDir branding link and theme toggle button
 */
export const ProfileFooter: React.FC<ProfileFooterProps> = ({
  className = "",
}) => {
  const { itemVariants } = useProfileAnimations();

  return (
    <motion.footer
      className={`mt-auto w-full border-t border-gray-100 pt-6 text-center text-sm text-gray-500 dark:border-gray-800 dark:text-gray-400 ${className}`}
      variants={itemVariants}
    >
      <p>
        Create your own directory with{" "}
        <a
          href="https://mydir.space"
          target="_blank"
          rel="noopener noreferrer"
          className="font-medium text-blue-600 transition-colors hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
        >
          MyDir
        </a>
      </p>
      {/* Theme Toggle - Centered horizontally */}
      <div className="mt-10 flex justify-center">
        <ThemeToggle />
      </div>
    </motion.footer>
  );
};
