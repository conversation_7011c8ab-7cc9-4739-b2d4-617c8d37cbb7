"use client";

import React from "react";
import Image from "next/image";
import { useYouTubeVideoFetcher } from "~/hooks/use_youtube_video_fetcher";
import { UrlInputForm } from "./components/url_input_form";
import { CenteredLoadingSpinner } from "./components/loading_spinner";
import DataPreview from "./components/data_preview";
import SectionHeader from "./components/section_header";
import { transformYouTubeVideoToCard } from "~/utils/card_transformers";

/**
 * YouTube video data extraction component
 */
export const YouTubeVideoExtractor: React.FC = () => {
  const { url, setUrl, data, loading, error, descriptionLinks, fetch, reset } =
    useYouTubeVideoFetcher();

  return (
    <div className="mb-6 border-t border-gray-200 pt-6 dark:border-gray-700">
      <SectionHeader
        title="YouTube Video Information"
        onReset={data ? reset : undefined}
      />

      <div className="space-y-4">
        <UrlInputForm
          url={url}
          onUrlChange={setUrl}
          onSubmit={fetch}
          loading={loading}
          error={error}
          placeholder="Enter YouTube video URL (e.g., https://www.youtube.com/watch?v=dQw4w9WgXcQ)"
          submitText="Fetch Video"
          submitColor="red"
        />

        {loading && (
          <CenteredLoadingSpinner text="Fetching video data..." size="lg" />
        )}

        {data && !loading && (
          <div className="space-y-4">
            {/* Video Preview */}
            {data.snippet?.thumbnails?.high?.url && (
              <div className="flex justify-center">
                <Image
                  src={data.snippet.thumbnails.high.url}
                  alt={data.snippet.title}
                  width={480}
                  height={360}
                  className="max-w-md rounded-md border dark:border-gray-600"
                />
              </div>
            )}

            {/* Video Info */}
            <div className="text-center">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                <a
                  href={`https://www.youtube.com/watch?v=${data.id}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-500 hover:underline"
                >
                  {data.snippet?.title}
                </a>
              </h3>
              <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                {data.snippet?.description?.substring(0, 300)}...
              </p>
            </div>

            {/* Description Links */}
            {descriptionLinks && descriptionLinks.length > 0 && (
              <div className="mt-3">
                <h4 className="text-md mb-2 font-semibold dark:text-white">
                  Links from Description:
                </h4>
                <ul className="list-inside list-disc space-y-1 text-sm">
                  {descriptionLinks.map((link, index) => (
                    <li key={index}>
                      <a
                        href={link}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-500 hover:text-blue-400 hover:underline"
                      >
                        {link}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Video Stats */}
            {data.statistics && (
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <strong>Views:</strong>{" "}
                  {parseInt(data.statistics.viewCount).toLocaleString()}
                </div>
                {data.statistics.likeCount && (
                  <div>
                    <strong>Likes:</strong>{" "}
                    {parseInt(data.statistics.likeCount).toLocaleString()}
                  </div>
                )}
                <div>
                  <strong>Channel:</strong> {data.snippet?.channelTitle}
                </div>
              </div>
            )}

            {/* Raw Data */}
            <DataPreview
              title="Raw Video Data"
              data={data}
              collapsible
              defaultCollapsed={true}
            />

            {/* Formatted JSON Card */}
            <DataPreview
              title="Formatted JSON Card"
              data={transformYouTubeVideoToCard(
                data,
                (data as any).channelAvatar || "",
                descriptionLinks || undefined,
              )}
              collapsible
              defaultCollapsed={true}
            />
          </div>
        )}
      </div>
    </div>
  );
};
