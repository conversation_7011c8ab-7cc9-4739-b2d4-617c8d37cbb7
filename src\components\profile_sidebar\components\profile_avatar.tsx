"use client";

import React from "react";
import Image from "next/image";
import { motion } from "framer-motion";
import { useProfileAnimations } from "../hooks/use_profile_animations";

interface ProfileAvatarProps {
  avatarUrl: string;
  name: string;
  className?: string;
}

/**
 * ProfileAvatar - User avatar component with gradient background and hover effects
 * Displays the user's profile image with animated gradient border and glow effect
 */
export const ProfileAvatar: React.FC<ProfileAvatarProps> = ({
  avatarUrl,
  name,
  className = "",
}) => {
  const { itemVariants } = useProfileAnimations();

  return (
    <motion.div
      className={`relative ${className}`}
      variants={itemVariants}
      whileHover={{ scale: 1.05 }}
      transition={{ duration: 0.3 }}
    >
      <div className="h-32 w-32 rounded-full bg-gradient-to-r from-blue-100 to-purple-100 p-1 dark:from-blue-900/50 dark:to-purple-900/50">
        <Image
          src={avatarUrl}
          alt={`${name} avatar`}
          className="h-full w-full rounded-full object-cover"
          width={128}
          height={128}
          draggable={false}
          priority
        />
      </div>
      <div className="absolute -inset-1 -z-10 rounded-full bg-gradient-to-r from-blue-200/20 to-purple-200/20 blur-md dark:from-blue-500/10 dark:to-purple-500/10" />
    </motion.div>
  );
};
