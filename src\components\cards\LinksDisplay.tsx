"use client";

import React from "react";
import { motion } from "framer-motion";
import { FaExternalLinkAlt, FaLink, FaGlobe, FaYoutube, FaGithub, FaTwitter } from "react-icons/fa";
import { cn } from "~/lib/utils";
import {
  linksContainerVariants,
  linkItemVariants,
} from "~/utils/card_flip_animations";

interface LinksDisplayProps {
  links: string[];
  className?: string;
  maxLinks?: number;
  showDomain?: boolean;
}

// Utility function to extract domain from URL
const extractDomain = (url: string): string => {
  try {
    const domain = new URL(url).hostname;
    return domain.replace(/^www\./, "");
  } catch {
    return url;
  }
};

// Utility function to get appropriate icon for URL
const getLinkIcon = (url: string) => {
  const domain = extractDomain(url).toLowerCase();
  
  if (domain.includes("youtube.com") || domain.includes("youtu.be")) {
    return FaYoutube;
  }
  if (domain.includes("github.com")) {
    return FaGithub;
  }
  if (domain.includes("twitter.com") || domain.includes("x.com")) {
    return FaTwitter;
  }
  if (domain.includes("http")) {
    return FaGlobe;
  }
  
  return FaLink;
};

// Utility function to get icon color based on domain
const getLinkIconColor = (url: string): string => {
  const domain = extractDomain(url).toLowerCase();
  
  if (domain.includes("youtube.com") || domain.includes("youtu.be")) {
    return "text-red-500";
  }
  if (domain.includes("github.com")) {
    return "text-gray-700 dark:text-gray-300";
  }
  if (domain.includes("twitter.com") || domain.includes("x.com")) {
    return "text-blue-400";
  }
  
  return "text-blue-500";
};

// Utility function to truncate URL for display
const truncateUrl = (url: string, maxLength: number = 50): string => {
  if (url.length <= maxLength) return url;
  
  const start = url.substring(0, maxLength - 10);
  const end = url.substring(url.length - 7);
  return `${start}...${end}`;
};

/**
 * LinksDisplay component for showing extracted links in an organized format
 * Matches the existing design system with proper accessibility features
 */
export const LinksDisplay: React.FC<LinksDisplayProps> = ({
  links,
  className,
  maxLinks = 8,
  showDomain = true,
}) => {
  // Limit the number of links displayed
  const displayLinks = links.slice(0, maxLinks);
  const hasMoreLinks = links.length > maxLinks;

  if (!links || links.length === 0) {
    return (
      <div className={cn("flex flex-col items-center justify-center py-8 text-center", className)}>
        <FaLink className="mb-3 text-2xl text-gray-400 dark:text-gray-500" />
        <p className="text-sm text-gray-500 dark:text-gray-400">
          No links found in video description
        </p>
      </div>
    );
  }

  return (
    <motion.div
      className={cn("flex flex-col space-y-3", className)}
      variants={linksContainerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Header */}
      <div className="flex items-center gap-2 mb-2">
        <FaLink className="text-sm text-gray-600 dark:text-gray-400" />
        <h4 className="text-sm font-semibold text-gray-900 dark:text-white">
          Links from Description ({links.length})
        </h4>
      </div>

      {/* Links list */}
      <div className="space-y-2 max-h-64 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600">
        {displayLinks.map((link, index) => {
          const IconComponent = getLinkIcon(link);
          const iconColor = getLinkIconColor(link);
          const domain = extractDomain(link);
          const displayUrl = truncateUrl(link);

          return (
            <motion.div
              key={`${link}-${index}`}
              variants={linkItemVariants}
              whileHover="hover"
              className="group"
            >
              <a
                href={link}
                target="_blank"
                rel="noopener noreferrer"
                className={cn(
                  "flex items-center gap-3 p-3 rounded-lg border transition-all duration-200",
                  "bg-gray-50 border-gray-200 hover:bg-gray-100 hover:border-gray-300",
                  "dark:bg-gray-800 dark:border-gray-700 dark:hover:bg-gray-700 dark:hover:border-gray-600",
                  "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
                  "dark:focus:ring-offset-gray-800"
                )}
                aria-label={`Open link: ${domain}`}
              >
                {/* Icon */}
                <div className={cn("flex-shrink-0", iconColor)}>
                  <IconComponent className="h-4 w-4" />
                </div>

                {/* Link content */}
                <div className="flex-1 min-w-0">
                  {showDomain && (
                    <div className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                      {domain}
                    </div>
                  )}
                  <div className="text-sm text-gray-900 dark:text-white truncate">
                    {displayUrl}
                  </div>
                </div>

                {/* External link icon */}
                <div className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <FaExternalLinkAlt className="h-3 w-3 text-gray-400 dark:text-gray-500" />
                </div>
              </a>
            </motion.div>
          );
        })}
      </div>

      {/* More links indicator */}
      {hasMoreLinks && (
        <motion.div
          variants={linkItemVariants}
          className="text-center py-2"
        >
          <p className="text-xs text-gray-500 dark:text-gray-400">
            +{links.length - maxLinks} more links available
          </p>
        </motion.div>
      )}
    </motion.div>
  );
};

export default LinksDisplay;
