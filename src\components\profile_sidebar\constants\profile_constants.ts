import type { IconSize as UtilIconSize } from "~/utils/socialIcons";

// Animation variants for the profile sidebar components
export const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2,
      duration: 0.5,
    },
  },
};

export const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: { duration: 0.4 },
  },
};

export const socialIconVariants = {
  hover: {
    scale: 1.1,
    rotate: 5,
    transition: { duration: 0.2 },
  },
  tap: { scale: 0.95 },
};

// Border radius variants for button styling
export const borderRadiusVariants = {
  none: "rounded-none",
  sm: "rounded-sm",
  md: "rounded-md",
  lg: "rounded-lg",
  xl: "rounded-xl",
  "2xl": "rounded-2xl",
  "3xl": "rounded-3xl",
  full: "rounded-full",
} as const;

export type BorderRadius = keyof typeof borderRadiusVariants;

// Button size variants for social icons
export const buttonSizeClasses = {
  sm: "h-10 w-10 p-2",
  md: "h-11 w-11 p-2.5",
  lg: "h-12 w-12 p-2",
} as const;

export type ButtonSize = keyof typeof buttonSizeClasses;

// Props interface for ProfileSidebarClient customization
export interface ProfileSidebarClientProps {
  /** Size of social media icons */
  iconSize?: UtilIconSize;
  /** Custom button size for social icons */
  buttonSize?: ButtonSize;
  /** Border radius for social icon buttons */
  borderRadius?: BorderRadius;
  /** Show enhanced hover effects */
  enhancedEffects?: boolean;
}

// Default props for ProfileSidebarClient
export const defaultProps: Required<ProfileSidebarClientProps> = {
  iconSize: "xl",
  buttonSize: "md",
  borderRadius: "full",
  enhancedEffects: true,
};
